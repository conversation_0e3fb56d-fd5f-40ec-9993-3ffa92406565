# 激光跟踪控制系统

基于STM32F4的双轴步进电机激光跟踪控制系统。

## 功能特性

### 核心功能
- **激光跟踪**: 通过MaixCam视觉模块识别红色和绿色激光点
- **双轴控制**: X轴和Y轴独立的步进电机控制
- **PID控制**: 位置式PID闭环控制算法
- **实时监控**: 电机位置、状态实时反馈

### 新增功能
- **初始上电180度转动**: 系统上电后自动执行XY轴各转动180度的初始化动作

## 硬件配置

### 通信接口
- UART1: 调试输出
- UART2: X轴步进电机通信 (Emm_V5驱动器)
- UART4: Y轴步进电机通信 (Emm_V5驱动器)
- UART5: MaixCam视觉模块通信

### 电机参数
- 最大转速: 3 RPM
- 最大角度范围: ±50°
- 180度对应脉冲数: 32768

## 使用说明

### 初始化流程
1. 系统上电初始化
2. 步进电机使能
3. 位置清零
4. **自动执行180度转动** (新增功能)
5. 保存初始位置
6. 进入激光跟踪模式

### 激光跟踪
- 数据格式: `red:(x,y)` 和 `gre:(x,y)`
- 控制逻辑: 绿色激光为目标，红色激光为当前位置
- PID参数: Kp=3, Ki=1, Kd=0.02

## 代码结构

```
├── Core/Src/main.c          # 主程序入口
├── bsp/                     # 板级支持包
│   ├── step_motor_bsp.c     # 步进电机控制
│   ├── pi_bsp.c            # 视觉数据处理
│   ├── uart_bsp.c          # 串口通信处理
│   └── schedule.c          # 任务调度器
├── app/                     # 应用层
│   ├── Emm_V5.c            # 步进电机驱动库
│   └── mypid.c             # PID控制算法
└── ringbuffer/             # 环形缓冲区
```

## 修改记录

### v1.2 - 新增初始上电180度转动功能
- 添加 `MOTOR_180_DEGREE_PULSE` 宏定义 (32768脉冲)
- 新增 `Step_Motor_Move_180_Degree()` 函数
- 在系统初始化流程中集成180度转动
- 增加延时至3秒确保大角度转动完成

### 技术细节
- 步进电机使用16位位置编码 (0-65535对应360°)
- 180度转动计算: 65536 × 180 ÷ 360 = 32768脉冲
- 转动方向: CW (顺时针)
- 转动速度: 3 RPM (最大速度)
- 测试效果: 半圈转动，视觉效果明显
