# 激光跟踪控制系统

基于STM32F4的双轴步进电机激光跟踪控制系统。

## 功能特性

### 核心功能
- **激光跟踪**: 通过MaixCam视觉模块识别红色和绿色激光点
- **双轴控制**: X轴和Y轴独立的步进电机控制
- **PID控制**: 位置式PID闭环控制算法
- **实时监控**: 电机位置、状态实时反馈

### 新增功能
- **初始上电演示序列**: 系统上电后自动执行多阶段转动演示
- **可配置角度转动**: 支持0-360度任意角度的精确转动
- **双向转动控制**: 支持顺时针(CW)和逆时针(CCW)转动

## 硬件配置

### 通信接口
- UART1: 调试输出
- UART2: X轴步进电机通信 (Emm_V5驱动器)
- UART4: Y轴步进电机通信 (Emm_V5驱动器)
- UART5: MaixCam视觉模块通信

### 电机参数
- 最大转速: 3 RPM
- 最大角度范围: ±50° (工作范围) / 0-360° (演示模式)
- 角度脉冲对应关系:
  - 90度: 16384脉冲
  - 180度: 32768脉冲
  - 360度: 65536脉冲

## 使用说明

### 初始化流程
1. 系统上电初始化
2. 步进电机使能
3. 位置清零
4. **自动执行演示序列** (新增功能):
   - 90度顺时针转动
   - 180度顺时针转动
   - 270度逆时针转动
   - 360度顺时针转动(完整一圈)
5. 保存初始位置
6. 进入激光跟踪模式

### 激光跟踪
- 数据格式: `red:(x,y)` 和 `gre:(x,y)`
- 控制逻辑: 绿色激光为目标，红色激光为当前位置
- PID参数: Kp=3, Ki=1, Kd=0.02

## 代码结构

```
├── Core/Src/main.c          # 主程序入口
├── bsp/                     # 板级支持包
│   ├── step_motor_bsp.c     # 步进电机控制
│   ├── pi_bsp.c            # 视觉数据处理
│   ├── uart_bsp.c          # 串口通信处理
│   └── schedule.c          # 任务调度器
├── app/                     # 应用层
│   ├── Emm_V5.c            # 步进电机驱动库
│   └── mypid.c             # PID控制算法
└── ringbuffer/             # 环形缓冲区
```

## 修改记录

### v1.3 - 增强型演示序列功能
- 添加多角度脉冲宏定义 (90°/180°/360°)
- 新增 `Step_Motor_Demo_Sequence()` 演示序列函数
- 新增 `Step_Motor_Move_Angle()` 通用角度转动函数
- 支持0-360度任意角度精确转动
- 支持双向转动控制 (CW/CCW)
- 分阶段演示效果，测试更加直观

### 技术细节
- 步进电机使用16位位置编码 (0-65535对应360°)
- 角度计算公式: 脉冲数 = 角度 × 65536 ÷ 360
- 演示序列: 90°→180°→270°→360°
- 转动方向: 可配置CW/CCW
- 转动速度: 3 RPM (最大速度)
- 测试效果: 多阶段渐进式演示，视觉效果极佳

### API接口
```c
// 执行完整演示序列
void Step_Motor_Demo_Sequence(void);

// 指定角度转动 (angle: 0-360度, direction: 0=CW/1=CCW)
void Step_Motor_Move_Angle(uint16_t angle, uint8_t direction);

// 180度转动 (兼容性保留)
void Step_Motor_Move_180_Degree(void);
```
